// 测试新的P6Spy灵活数据库名称映射功能
// 此文件用于验证配置是否正确工作

import com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger;
import java.util.HashMap;
import java.util.Map;

public class P6SpyFlexibleMappingTest {
    
    public static void main(String[] args) {
        System.out.println("=== P6Spy 灵活数据库名称映射测试 ===\n");
        
        // 测试不同的配置场景
        testOriginalNameMode();
        testPrefixMode();
        testCustomMappingMode();
        testMixedMode();
        testDynamicMapping();
    }
    
    /**
     * 测试使用原始数据库名称模式
     */
    private static void testOriginalNameMode() {
        System.out.println("--- 测试1: 使用原始数据库名称 ---");
        
        // 模拟配置: useOriginalName=true
        System.setProperty("p6spy.database.useOriginalName", "true");
        
        // 测试不同的数据库名称
        String[] testDatabases = {"shanghai", "dev_config_db", "test_user", "prod_acct"};
        
        for (String dbName : testDatabases) {
            String result = simulateMapping(dbName);
            System.out.printf("  %-15s -> %s%n", dbName, result);
        }
        
        System.out.println("预期: 显示原始数据库名称\n");
    }
    
    /**
     * 测试前缀模式
     */
    private static void testPrefixMode() {
        System.out.println("--- 测试2: 统一前缀模式 ---");
        
        // 模拟配置: useOriginalName=false, namePrefix="YL_", enableMapping=false
        System.setProperty("p6spy.database.useOriginalName", "false");
        System.setProperty("p6spy.database.namePrefix", "YL_");
        System.setProperty("p6spy.database.enableMapping", "false");
        
        String[] testDatabases = {"shanghai", "config", "user", "acct"};
        
        for (String dbName : testDatabases) {
            String result = simulateMapping(dbName);
            System.out.printf("  %-15s -> %s%n", dbName, result);
        }
        
        System.out.println("预期: 添加YL_前缀\n");
    }
    
    /**
     * 测试自定义映射模式
     */
    private static void testCustomMappingMode() {
        System.out.println("--- 测试3: 自定义映射模式 ---");
        
        // 模拟配置: enableMapping=true, 自定义映射
        System.setProperty("p6spy.database.useOriginalName", "false");
        System.setProperty("p6spy.database.namePrefix", "");
        System.setProperty("p6spy.database.enableMapping", "true");
        
        // 模拟映射配置
        Map<String, String> mappings = new HashMap<>();
        mappings.put("shanghai", "CONFIG");
        mappings.put("dev_user_db", "USER");
        mappings.put("test_acct", "ACCT");
        mappings.put("prod_social", "SOCIAL");
        
        // 添加映射
        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            P6SpyLogger.addDatabaseMapping(entry.getKey(), entry.getValue());
        }
        
        String[] testDatabases = {"shanghai", "dev_user_db", "test_acct", "prod_social", "unknown_db"};
        
        for (String dbName : testDatabases) {
            String result = simulateMapping(dbName);
            System.out.printf("  %-15s -> %s%n", dbName, result);
        }
        
        System.out.println("预期: 使用自定义映射，未配置的使用提取的名称\n");
    }
    
    /**
     * 测试混合模式
     */
    private static void testMixedMode() {
        System.out.println("--- 测试4: 混合模式（映射+前缀）---");
        
        // 模拟配置: enableMapping=true, namePrefix="YL_"
        System.setProperty("p6spy.database.useOriginalName", "false");
        System.setProperty("p6spy.database.namePrefix", "YL_");
        System.setProperty("p6spy.database.enableMapping", "true");
        
        // 清除之前的映射，添加新的
        P6SpyLogger.reloadDatabaseMappings();
        P6SpyLogger.addDatabaseMapping("shanghai", "CONFIG");
        P6SpyLogger.addDatabaseMapping("special_db", "SPECIAL");
        
        String[] testDatabases = {"shanghai", "special_db", "dev_user_db", "test_log"};
        
        for (String dbName : testDatabases) {
            String result = simulateMapping(dbName);
            System.out.printf("  %-15s -> %s%n", dbName, result);
        }
        
        System.out.println("预期: 有映射的使用映射+前缀，无映射的使用提取+前缀\n");
    }
    
    /**
     * 测试动态映射
     */
    private static void testDynamicMapping() {
        System.out.println("--- 测试5: 动态映射更新 ---");
        
        String testDb = "dynamic_test_db";
        
        // 初始状态
        String result1 = simulateMapping(testDb);
        System.out.printf("添加映射前: %-15s -> %s%n", testDb, result1);
        
        // 动态添加映射
        P6SpyLogger.addDatabaseMapping(testDb, "DYNAMIC");
        String result2 = simulateMapping(testDb);
        System.out.printf("添加映射后: %-15s -> %s%n", testDb, result2);
        
        // 重新加载配置
        P6SpyLogger.reloadDatabaseMappings();
        String result3 = simulateMapping(testDb);
        System.out.printf("重新加载后: %-15s -> %s%n", testDb, result3);
        
        System.out.println("预期: 动态添加的映射生效，重新加载后恢复默认\n");
    }
    
    /**
     * 模拟数据库名称映射
     * 注意：这是一个简化的模拟，实际的映射逻辑在P6SpyLogger中
     */
    private static String simulateMapping(String dbName) {
        // 这里只是演示，实际应该调用P6SpyLogger的相关方法
        // 由于mapToLogicalDatabaseName是私有方法，这里做简化处理
        
        boolean useOriginalName = Boolean.parseBoolean(System.getProperty("p6spy.database.useOriginalName", "false"));
        String namePrefix = System.getProperty("p6spy.database.namePrefix", "");
        boolean enableMapping = Boolean.parseBoolean(System.getProperty("p6spy.database.enableMapping", "true"));
        
        if (useOriginalName) {
            return dbName;
        }
        
        String result = dbName;
        
        // 简化的映射逻辑
        if (enableMapping) {
            // 这里应该检查实际的映射配置
            if ("shanghai".equals(dbName)) {
                result = "CONFIG";
            } else if (dbName.contains("user")) {
                result = "USER";
            } else if (dbName.contains("acct")) {
                result = "ACCT";
            } else if (dbName.contains("social")) {
                result = "SOCIAL";
            } else {
                // 提取业务标识符
                result = extractBusinessIdentifier(dbName);
            }
        } else {
            result = extractBusinessIdentifier(dbName);
        }
        
        // 添加前缀
        if (!namePrefix.isEmpty() && !result.toUpperCase().startsWith(namePrefix.toUpperCase())) {
            result = namePrefix + result;
        }
        
        return result.toUpperCase();
    }
    
    /**
     * 简化的业务标识符提取
     */
    private static String extractBusinessIdentifier(String dbName) {
        String cleaned = dbName
            .replaceAll("^(dev_|test_|prod_|staging_|local_)", "")
            .replaceAll("(_db|_database)$", "")
            .replaceAll("^yl_", "");
        
        return cleaned.isEmpty() ? dbName : cleaned;
    }
}

// 使用方法：
// 1. 将此文件保存为 P6SpyFlexibleMappingTest.java
// 2. 编译并运行：javac P6SpyFlexibleMappingTest.java && java P6SpyFlexibleMappingTest
// 3. 观察不同配置模式下的映射结果
// 4. 根据需要调整nacos配置
