# P6Spy 数据库名称映射配置示例
# 此文件展示了如何在nacos中配置P6Spy的数据库名称映射

# ==================== P6Spy 数据库配置 ====================

# 是否使用原始数据库名称（不进行任何转换）
# 默认: false
p6spy:
  database:
    useOriginalName: false
    
    # 数据库名称前缀（可选）
    # 如果设置了前缀，会自动添加到数据库名称前面
    # 例如: namePrefix: "YL_" 会将 "config" 转换为 "YL_CONFIG"
    namePrefix: "YL_"
    
    # 是否启用数据库名称映射
    # 默认: true
    enableMapping: true
    
    # 数据库名称映射配置
    # 格式: mappings.{物理数据库名} = {逻辑数据库名}
    mappings:
      # 具体的数据库名称映射
      shanghai: "CONFIG"
      dev_config_db: "CONFIG"
      test_config: "CONFIG"
      prod_config: "CONFIG"
      
      dev_user_db: "USER"
      test_user: "USER"
      prod_user: "USER"
      
      dev_acct_db: "ACCT"
      test_acct: "ACCT"
      prod_acct: "ACCT"
      
      dev_social_db: "SOCIAL"
      test_social: "SOCIAL"
      prod_social: "SOCIAL"
      
      dev_log_db: "LOG"
      test_log: "LOG"
      prod_log: "LOG"
      
      dev_busi_db: "BUSI"
      test_busi: "BUSI"
      prod_busi: "BUSI"

# ==================== 配置说明 ====================

# 1. 基本配置模式
# 如果只想使用原始数据库名称，不做任何转换：
# p6spy:
#   database:
#     useOriginalName: true

# 2. 简单前缀模式
# 如果想给所有数据库名称添加统一前缀：
# p6spy:
#   database:
#     useOriginalName: false
#     namePrefix: "YL_"
#     enableMapping: false

# 3. 完全自定义映射模式
# 如果需要完全自定义每个数据库的显示名称：
# p6spy:
#   database:
#     useOriginalName: false
#     namePrefix: ""
#     enableMapping: true
#     mappings:
#       shanghai: "CONFIG"
#       dev_user_db: "USER_SERVICE"

# ==================== 配置优先级 ====================

# 配置处理优先级（从高到低）：
# 1. useOriginalName=true: 直接使用原始数据库名称
# 2. enableMapping=true 且存在映射: 使用配置的映射
# 3. 自动提取: 从数据库名称中提取业务标识符

# ==================== 日志格式示例 ====================

# 配置前（硬编码）:
# [SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: YL_CONFIG | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345

# 配置后（灵活配置）:
# 1. useOriginalName=true:
#    [SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: shanghai | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345

# 2. namePrefix="YL_", enableMapping=false:
#    [SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: YL_SHANGHAI | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345

# 3. 自定义映射:
#    [SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: CONFIG | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345

# ==================== 动态配置更新 ====================

# 支持运行时动态更新配置，无需重启应用
# 可以通过nacos配置中心实时修改数据库名称映射
