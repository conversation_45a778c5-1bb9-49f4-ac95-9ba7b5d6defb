import com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger;

/**
 * 简化版P6Spy测试
 */
public class TestP6SpySimple {
    public static void main(String[] args) {
        System.out.println("=== 简化版P6Spy测试 ===\n");
        
        // 测试不同环境
        testEnvironment("dev");
        testEnvironment("test");
        testEnvironment("prod");
        testEnvironment("staging");
        
        // 测试数据库映射
        testDatabaseMapping();
    }

    private static void testEnvironment(String env) {
        System.out.println("--- 环境: " + env + " ---");
        
        // 设置环境
        System.setProperty("spring.profiles.active", env);
        
        // 创建实例测试
        P6SpyLogger logger = new P6SpyLogger();
        
        // 模拟SQL日志
        String result = logger.formatMessage(
            1,
            "2025-07-05 15:30:00",
            25,
            "statement",
            "",
            "SELECT * FROM t_user WHERE user_id = ?",
            "*****************************************"
        );
        
        System.out.println("日志输出: " + result);
        System.out.println();
        
        // 清理环境变量
        System.clearProperty("spring.profiles.active");
    }

    private static void testDatabaseMapping() {
        System.out.println("=== 数据库映射测试 ===");
        
        P6SpyLogger logger = new P6SpyLogger();
        
        // 测试各种数据库名
        String[] testUrls = {
            "*******************************************",
            "*****************************************", 
            "*****************************************",
            "***********************************************",
            "**********************************************",
            "********************************************"
        };
        
        for (String url : testUrls) {
            String result = logger.formatMessage(
                1,
                "2025-07-05 15:30:00",
                15,
                "statement",
                "",
                "SELECT 1",
                url
            );
            System.out.println("URL: " + url);
            System.out.println("结果: " + result);
            System.out.println();
        }
    }
}
