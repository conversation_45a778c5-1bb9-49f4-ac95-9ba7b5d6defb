# P6Spy 灵活数据库映射使用示例

## 快速开始

### 1. 最简配置（推荐新项目）

如果您希望直接显示数据库的真实名称，无需任何转换：

**nacos配置 (p6spy-config.yaml)**:
```yaml
p6spy:
  database:
    useOriginalName: true
```

**效果**:
```
[SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: shanghai | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345
[SQL-DEV] 2025-07-05 10:30:16 | 执行时间: 12ms | 数据库: dev_user_db | 调用: UserService.findUser:23 | SQL: SELECT * FROM t_user_info WHERE id = 1
```

### 2. 保持现有格式（兼容旧版本）

如果您想保持与之前硬编码版本相同的显示效果：

**nacos配置**:
```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "YL_"
    enableMapping: true
    mappings:
      shanghai: "CONFIG"
      dev_user_db: "USER"
      test_user: "USER"
      prod_user: "USER"
      dev_acct_db: "ACCT"
      test_acct: "ACCT"
      prod_acct: "ACCT"
```

**效果**:
```
[SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: YL_CONFIG | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345
[SQL-DEV] 2025-07-05 10:30:16 | 执行时间: 12ms | 数据库: YL_USER | 调用: UserService.findUser:23 | SQL: SELECT * FROM t_user_info WHERE id = 1
```

### 3. 简洁业务标识（推荐）

显示简洁的业务模块名称，便于快速识别：

**nacos配置**:
```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: ""
    enableMapping: true
    mappings:
      shanghai: "CONFIG"
      dev_user_db: "USER"
      dev_acct_db: "ACCT"
      dev_social_db: "SOCIAL"
      dev_log_db: "LOG"
```

**效果**:
```
[SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: CONFIG | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345
[SQL-DEV] 2025-07-05 10:30:16 | 执行时间: 12ms | 数据库: USER | 调用: UserService.findUser:23 | SQL: SELECT * FROM t_user_info WHERE id = 1
```

## 高级配置

### 环境特定配置

**开发环境** (dev环境nacos配置):
```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "DEV_"
    enableMapping: true
    mappings:
      shanghai: "CONFIG"
      user_db: "USER"
```

**测试环境** (test环境nacos配置):
```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "TEST_"
    enableMapping: true
    mappings:
      test_config: "CONFIG"
      test_user: "USER"
```

### 混合策略配置

只为特殊数据库配置映射，其他使用自动提取：

```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "YL_"
    enableMapping: true
    mappings:
      # 只配置特殊的数据库
      shanghai: "CONFIG"
      legacy_db: "LEGACY"
      # 其他数据库会自动从名称中提取业务标识符
```

**效果**:
- `shanghai` → `YL_CONFIG`
- `legacy_db` → `YL_LEGACY`
- `dev_user_db` → `YL_USER_DB` (自动提取)
- `test_acct` → `YL_ACCT` (自动提取)

## 动态配置管理

### 运行时添加映射

```java
// 在应用启动时或配置更新时
@PostConstruct
public void initDatabaseMappings() {
    // 添加特殊的数据库映射
    P6SpyLogger.addDatabaseMapping("custom_db", "CUSTOM");
    P6SpyLogger.addDatabaseMapping("temp_db", "TEMP");
}
```

### 配置热更新

通过nacos配置中心修改配置后，应用会自动刷新：

```java
@Component
public class P6SpyConfigRefreshImpl implements RefreshableConfig {
    @Override
    public String dataId() {
        return "p6spy-config.yaml";
    }

    @Override
    public void receiveConfigInfo(Properties props) {
        // 配置更新时自动重新加载
        P6SpyLogger.reloadDatabaseMappings();
    }
}
```

## 实际项目配置示例

### 微服务项目配置

```yaml
# 适用于微服务架构，每个服务有独立的数据库
p6spy:
  database:
    useOriginalName: false
    namePrefix: ""
    enableMapping: true
    mappings:
      # 用户服务
      user_service_db: "USER_SVC"
      user_profile_db: "USER_PROFILE"
      
      # 订单服务
      order_service_db: "ORDER_SVC"
      order_history_db: "ORDER_HIST"
      
      # 支付服务
      payment_service_db: "PAY_SVC"
      payment_log_db: "PAY_LOG"
```

### 单体应用配置

```yaml
# 适用于单体应用，按业务模块划分数据库
p6spy:
  database:
    useOriginalName: false
    namePrefix: "APP_"
    enableMapping: true
    mappings:
      main_db: "MAIN"
      config_db: "CONFIG"
      log_db: "LOG"
      cache_db: "CACHE"
```

## 故障排除

### 1. 配置不生效

**问题**: 修改了nacos配置但日志格式没有变化

**解决方案**:
```java
// 手动触发配置重新加载
P6SpyLogger.reloadDatabaseMappings();
```

**检查步骤**:
1. 确认nacos配置文件名正确 (`p6spy-config.yaml`)
2. 检查配置格式是否正确
3. 查看应用启动日志中的配置加载信息

### 2. 映射不正确

**问题**: 数据库名称映射结果不符合预期

**调试方法**:
```java
// 测试特定数据库的映射结果
String result = P6SpyMappingManager.testMapping("your_database_name");
System.out.println("映射结果: " + result);
```

### 3. 性能考虑

**优化建议**:
- 映射结果会被缓存，首次访问后性能影响很小
- 避免过于复杂的映射规则
- 生产环境建议禁用P6Spy

## 迁移指南

### 从硬编码版本迁移

**步骤1**: 备份现有配置
```java
// 记录当前的硬编码映射关系
// shanghai -> YL_CONFIG
// dev_user_db -> YL_USER
// ...
```

**步骤2**: 创建等效的nacos配置
```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "YL_"
    enableMapping: true
    mappings:
      shanghai: "CONFIG"
      dev_user_db: "USER"
      # 添加所有现有的映射关系
```

**步骤3**: 验证配置
- 部署到测试环境
- 执行SQL操作
- 检查日志格式是否与之前一致

**步骤4**: 逐步优化
- 移除不必要的映射
- 简化配置规则
- 考虑使用更简洁的显示方式

## 最佳实践

1. **新项目**: 推荐使用 `useOriginalName: true`，简单直接
2. **现有项目**: 先保持兼容，再逐步简化
3. **团队协作**: 统一配置规范，避免混乱
4. **环境隔离**: 不同环境使用不同的前缀或映射
5. **文档维护**: 及时更新数据库映射文档
