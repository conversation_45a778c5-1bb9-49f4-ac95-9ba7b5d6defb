import com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger;

/**
 * 测试动态数据库名推断功能
 */
public class TestDynamicMapping {
    public static void main(String[] args) {
        System.out.println("=== P6Spy 动态数据库名推断测试 ===\n");
        
        P6SpyLogger logger = new P6SpyLogger();
        
        // 测试各种数据库名格式
        String[] testUrls = {
            // 标准YL_格式
            "*******************************************",
            "*****************************************", 
            "*****************************************",
            
            // 带环境前缀
            "***********************************************",
            "**********************************************",
            "**********************************************",
            
            // 不同命名风格
            "*****************************************",
            "***************************************************",
            "****************************************_center",
            "*************************************************",
            
            // 带环境前缀的不同风格
            "*********************************************",
            "**************************************************",
            "***************************************************",
            
            // 简化命名
            "****************************************",
            "**************************************",
            "**************************************",
            "**************************************",
            
            // 其他业务模块
            "*******************************************",
            "********************************************",
            "********************************************",
            "*******************************************",
            "***********************************************",
            "********************************************",
            
            // 无法识别的数据库名
            "************************************************",
            "********************************************",
            "********************************************"
        };
        
        System.out.println("数据库名动态推断结果：");
        System.out.println("原始数据库名 -> 推断的逻辑名");
        System.out.println("=" * 50);
        
        for (String url : testUrls) {
            String result = logger.formatMessage(
                1,
                "2025-07-05 15:30:00",
                15,
                "statement",
                "",
                "SELECT 1",
                url
            );
            
            // 提取数据库名部分
            String dbName = extractDbNameFromUrl(url);
            String logicalName = extractLogicalNameFromResult(result);
            
            System.out.printf("%-25s -> %s%n", dbName, logicalName);
        }
        
        System.out.println("\n=== 缓存效果测试 ===");
        System.out.println("第二次访问相同数据库（应该使用缓存）：");
        
        // 测试缓存效果
        String testUrl = "**************************************************";
        
        long start1 = System.nanoTime();
        String result1 = logger.formatMessage(1, "2025-07-05 15:30:00", 15, "statement", "", "SELECT 1", testUrl);
        long time1 = System.nanoTime() - start1;
        
        long start2 = System.nanoTime();
        String result2 = logger.formatMessage(1, "2025-07-05 15:30:00", 15, "statement", "", "SELECT 1", testUrl);
        long time2 = System.nanoTime() - start2;
        
        System.out.println("第一次推断耗时: " + time1 + "ns");
        System.out.println("第二次推断耗时: " + time2 + "ns");
        System.out.println("缓存加速比: " + (time1 / (double)time2) + "x");
    }
    
    private static String extractDbNameFromUrl(String url) {
        int lastSlash = url.lastIndexOf('/');
        if (lastSlash != -1 && lastSlash < url.length() - 1) {
            return url.substring(lastSlash + 1);
        }
        return url;
    }
    
    private static String extractLogicalNameFromResult(String result) {
        // 从结果中提取数据库名部分
        int dbStart = result.indexOf("数据库: ");
        if (dbStart != -1) {
            dbStart += 4; // "数据库: ".length()
            int dbEnd = result.indexOf(" |", dbStart);
            if (dbEnd != -1) {
                return result.substring(dbStart, dbEnd);
            }
        }
        return "UNKNOWN";
    }
}
