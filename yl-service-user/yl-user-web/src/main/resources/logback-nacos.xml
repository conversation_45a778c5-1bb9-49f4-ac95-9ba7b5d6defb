<configuration>
    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="logPath" source="logging.logback.logPath"/>
    <springProperty scope="context" name="logstashEndpoint" source="logging.logstash.endpoint"/>
    <springProperty scope="context" name="loghubProject" source="logging.loghub.project"/>
    <springProperty scope="context" name="loghubLogstore" source="logging.loghub.logstore"/>
    <springProperty scope="context" name="loghubEndpoint" source="logging.loghub.endpoint"/>
    <springProperty scope="context" name="loghubAccessKey" source="logging.loghub.accessKey"/>
    <springProperty scope="context" name="loghubAccessSecret" source="logging.loghub.accessSecret"/>

    <!-- 开发环境设置 -->
    <springProfile name="dev">
        <!-- 输出到控制台 -->
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{MM-dd HH:mm:ss} [%thread-%level] %logger{100}:%line -> %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="STDOUT"/>
        </root>

    </springProfile>
    <!-- 测试环境设置 -->
    <springProfile name="test">
        <!-- 输出到控制台 -->
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{MM-dd HH:mm:ss} [%thread-%level] %logger{100}:%line -> %msg%n</pattern>
            </encoder>
        </appender>
        <root level="WARN">
            <appender-ref ref="STDOUT"/>
        </root>

        <!-- 输出到日志文件 -->
        <appender name="ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 文件路径 -->
            <file>${logPath}/${appName}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <!-- rollover daily -->
                <fileNamePattern>${logPath}/${appName}_%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <!-- 每个日志文件最大100MB, 保留3天的日志文件, 但是最多总文件大小为 5GB -->
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>3</maxHistory>
                <totalSizeCap>5GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread-%level] %logger{0}:%line -> %msg%n</pattern>
            </encoder>
        </appender>
        <!-- 输出到es里
        <appender name="LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
            <param name="Encoding" value="UTF-8"/>
            <destination>${logging.logstash.endpoint}</destination>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [${appName}] [%level] %logger{0}:%line -> %msg%n</pattern>
            </encoder> -->
        <!-- 只接收INFO级别以上的日志
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>-->
        <root level="WARN">
            <appender-ref ref="ROLLING"/>
            <!--
            <appender-ref ref="LOGSTASH"/>
            -->
        </root>
    </springProfile>
    <!-- 生产环境设置 -->
    <springProfile name="prod">
        <!-- 输出到控制台-仅为迁移使用 -->
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{MM-dd HH:mm:ss} [%thread-%level] %logger{100}:%line -> %msg%n</pattern>
            </encoder>
        </appender>
        <!-- 输出到日志文件 -->
        <appender name="ROLLING" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <!-- 文件路径 -->
            <file>${logPath}/${appName}.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                <!-- rollover daily -->
                <fileNamePattern>${logPath}/${appName}_%d{yyyy-MM-dd}.%i.gz</fileNamePattern>
                <!-- 每个日志文件最大100MB, 保留3天的日志文件, 但是最多总文件大小为 5GB -->
                <maxFileSize>100MB</maxFileSize>
                <maxHistory>3</maxHistory>
                <totalSizeCap>5GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread-%level] %logger{0}:%line -> %msg%n</pattern>
            </encoder>
        </appender>
        <!-- 输出日志到阿里云(只记录INFO日志) -->
        <appender name="LOGHUB-INFO" class="com.aliyun.openservices.log.logback.LoghubAppender">
            <topic>${appName}</topic>
            <projectName>${loghubProject}</projectName>
            <endpoint>${loghubEndpoint}</endpoint>
            <accessKeyId>${loghubAccessKey}</accessKeyId>
            <accessKey>${loghubAccessSecret}</accessKey>
            <logstore>${loghubLogstore}</logstore>
            <timeFormat>yyyy-MM-dd HH:mm:ss SSS</timeFormat>
            <timeZone>Asia/Shanghai</timeZone>
<!--            <filter class="ch.qos.logback.classic.filter.LevelFilter">-->
<!--                <level>INFO</level>-->
<!--                <onMatch>ACCEPT</onMatch>-->
<!--                <onMismatch>DENY</onMismatch>-->
<!--            </filter>-->
        </appender>
        <!-- 输出日志到阿里云(记录WARN级别以上日志) -->
<!--        <appender name="LOGHUB-LOGBACK" class="com.aliyun.openservices.log.logback.LoghubAppender">-->
<!--            <topic>${appName}</topic>-->
<!--            <projectName>${loghubProject}</projectName>-->
<!--            <endpoint>${loghubEndpoint}</endpoint>-->
<!--            <accessKeyId>${loghubAccessKey}</accessKeyId>-->
<!--            <accessKey>${loghubAccessSecret}</accessKey>-->
<!--            <logstore>${loghubLogstore}</logstore>-->
<!--            <timeFormat>yyyy-MM-dd HH:mm:ss SSS</timeFormat>-->
<!--            <timeZone>Asia/Shanghai</timeZone>-->
<!--            <filter class="ch.qos.logback.classic.filter.ThresholdFilter">-->
<!--                <level>WARN</level>-->
<!--            </filter>-->
<!--        </appender>-->
        <root level="WARN">
            <appender-ref ref="STDOUT"/>
            <appender-ref ref="ROLLING"/>
            <appender-ref ref="LOGHUB-INFO"/>
<!--            <appender-ref ref="LOGHUB-LOGBACK"/>-->
        </root>
    </springProfile>

    <logger name="com.tuowan" level="INFO"/>
    <logger name="com.easyooo.framework" level="ERROR"/>
    <logger name="com.easyooo.framework.support.redis.jedis" level="INFO"/>
    <logger name="com.alibaba.cloud.nacos.registry.NacosServiceRegistry" level="INFO"/>
    <logger name="org.springframework.boot.web.embedded.tomcat.TomcatWebServer" level="INFO"/>

</configuration>