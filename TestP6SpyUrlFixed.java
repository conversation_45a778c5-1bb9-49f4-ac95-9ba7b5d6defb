// Test fixed P6Spy URL conversion logic
public class TestP6SpyUrlFixed {
    public static void main(String[] args) {
        System.out.println("=== Testing Fixed P6Spy URL Conversion ===\n");
        
        // Test case 1: Standard MySQL URL
        testUrlConversion(
            "*****************************************************************************************************************",
            "mysql://**************:4417/yl_config?useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true"
        );
        
        // Test case 2: URL without jdbc: prefix
        testUrlConversion(
            "mysql://localhost:3306/test",
            "mysql://localhost:3306/test"
        );
        
        // Test case 3: URL already with p6spy prefix
        testUrlConversion(
            "**************************************",
            "mysql://localhost:3306/test"
        );
        
        // Test case 4: PostgreSQL URL
        testUrlConversion(
            "*************************************",
            "postgresql://localhost:5432/test"
        );
    }
    
    private static void testUrlConversion(String originalUrl, String expectedResult) {
        System.out.println("Original URL: " + originalUrl);
        
        String jdbcUrl = originalUrl;
        
        // Apply the fixed conversion logic
        if (!jdbcUrl.startsWith("jdbc:p6spy:")) {
            // Remove existing jdbc: prefix, then add p6spy prefix
            if (jdbcUrl.startsWith("jdbc:")) {
                jdbcUrl = "jdbc:p6spy:" + jdbcUrl.substring(5);
            } else {
                jdbcUrl = "jdbc:p6spy:" + jdbcUrl;
            }
        }
        
        System.out.println("P6Spy URL: " + jdbcUrl);
        System.out.println("Expected: jdbc:p6spy:" + expectedResult);
        
        // Validate result
        String expected = "jdbc:p6spy:" + expectedResult;
        if (jdbcUrl.equals(expected)) {
            System.out.println("PASS: Conversion is correct");
        } else {
            System.out.println("FAIL: Conversion is incorrect");
        }

        // Check for double prefix
        if (jdbcUrl.contains("jdbc:jdbc:")) {
            System.out.println("ERROR: Double jdbc: prefix detected!");
        } else {
            System.out.println("OK: No double prefix");
        }
        
        System.out.println("---");
    }
}
