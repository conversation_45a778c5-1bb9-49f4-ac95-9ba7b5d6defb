# 模块列表，使用默认配置进行测试
modulelist=com.p6spy.engine.logging.P6LogFactory

# 使用默认日志格式进行测试
#logMessageFormat=com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger

# 使用默认控制台输出进行测试
appender=com.p6spy.engine.spy.appender.StdoutLogger

# 取消JDBC驱动注册
deregisterdrivers=true

# 使用前缀
useprefix=true

# 排除的日志类别
excludecategories=info,debug,result,commit,resultset

# 日期格式
dateformat=yyyy-MM-dd HH:mm:ss

# 实际驱动列表
# driverlist=org.h2.Driver

# 开启慢SQL记录
outagedetection=true

# 慢SQL记录标准（单位：秒）
outagedetectioninterval=2

# Disable filtering for testing
filter=false
#exclude=SELECT 1