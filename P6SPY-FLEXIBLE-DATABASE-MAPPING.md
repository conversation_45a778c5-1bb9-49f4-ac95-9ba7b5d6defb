# P6Spy 灵活数据库名称映射配置

## 概述

新的P6Spy实现移除了硬编码的数据库名称映射，支持通过nacos配置灵活控制数据库名称的显示方式，提供了更好的扩展性和可维护性。

## 配置方式

### 1. 使用原始数据库名称

如果您希望在日志中显示原始的数据库名称（如 `shanghai`、`dev_config_db`），可以配置：

```yaml
p6spy:
  database:
    useOriginalName: true
```

**日志效果**：
```
[SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: shanghai | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345
```

### 2. 添加统一前缀

如果您希望给所有数据库名称添加统一前缀：

```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "YL_"
    enableMapping: false
```

**日志效果**：
```
[SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: YL_SHANGHAI | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345
```

### 3. 自定义数据库名称映射

如果您需要完全自定义每个数据库的显示名称：

```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: ""  # 可选，如果映射中已包含前缀
    enableMapping: true
    mappings:
      # 物理数据库名 -> 逻辑显示名
      shanghai: "CONFIG"
      dev_config_db: "CONFIG"
      test_config: "CONFIG"
      prod_config: "CONFIG"
      
      dev_user_db: "USER"
      test_user: "USER"
      prod_user: "USER"
      
      dev_acct_db: "ACCT"
      test_acct: "ACCT"
      prod_acct: "ACCT"
```

**日志效果**：
```
[SQL-DEV] 2025-07-05 10:30:15 | 执行时间: 25ms | 数据库: CONFIG | 调用: UserMapper.selectById:45 | SQL: SELECT * FROM t_user WHERE user_id = 12345
```

### 4. 混合配置（推荐）

结合前缀和映射，既保持一致性又支持特殊情况：

```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "YL_"
    enableMapping: true
    mappings:
      # 只配置特殊的映射，其他使用自动提取+前缀
      shanghai: "CONFIG"
      special_db: "SPECIAL"
```

## 配置优先级

配置处理按以下优先级执行：

1. **useOriginalName=true**: 直接使用原始数据库名称，忽略其他配置
2. **enableMapping=true 且存在映射**: 使用配置的映射关系
3. **自动提取**: 从数据库名称中提取业务标识符，然后应用前缀

## 动态配置更新

支持通过nacos配置中心实时更新配置，无需重启应用：

1. 在nacos中修改P6Spy配置
2. 配置会自动刷新并生效
3. 新的SQL日志将使用更新后的配置

## 编程方式配置

除了配置文件，还支持通过代码动态添加映射：

```java
// 添加单个映射
P6SpyLogger.addDatabaseMapping("custom_db", "CUSTOM");

// 重新加载配置
P6SpyLogger.reloadDatabaseMappings();
```

## 配置示例

### 开发环境配置
```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "DEV_"
    enableMapping: true
    mappings:
      shanghai: "CONFIG"
      user_db: "USER"
```

### 测试环境配置
```yaml
p6spy:
  database:
    useOriginalName: false
    namePrefix: "TEST_"
    enableMapping: true
    mappings:
      test_config: "CONFIG"
      test_user: "USER"
```

### 简化配置（只使用原始名称）
```yaml
p6spy:
  database:
    useOriginalName: true
```

## 迁移指南

### 从硬编码迁移

如果您之前使用的是硬编码版本，迁移步骤：

1. **保持现有行为**：
   ```yaml
   p6spy:
     database:
       useOriginalName: false
       namePrefix: "YL_"
       enableMapping: true
       mappings:
         shanghai: "CONFIG"
         # 添加其他现有的映射
   ```

2. **简化配置**：
   ```yaml
   p6spy:
     database:
       useOriginalName: true
   ```

3. **自定义配置**：根据实际需求配置映射关系

## 注意事项

1. **配置文件位置**: 配置应放在nacos配置中心，文件名为 `p6spy-config.yaml`
2. **大小写**: 物理数据库名会自动转换为小写进行匹配，逻辑名会转换为大写显示
3. **缓存**: 映射结果会被缓存以提高性能，配置更新时会自动清除缓存
4. **兼容性**: 新配置完全向后兼容，不影响现有功能

## 故障排除

### 配置不生效
1. 检查nacos配置文件名是否正确
2. 确认配置格式是否正确
3. 查看应用日志中的配置加载信息

### 映射不正确
1. 检查物理数据库名是否正确（区分大小写）
2. 确认配置优先级是否符合预期
3. 使用编程方式测试映射结果

### 性能问题
1. 映射结果会被缓存，首次访问后性能影响很小
2. 如果配置过于复杂，考虑简化映射规则
