package com.tuowan.yeliao.commons.config.p6spy;

import org.junit.Test;

/**
 * P6SpyLogger测试类
 */
public class P6SpyLoggerTest {

    @Test
    public void testFormatMessage() {
        // 设置测试环境
        System.setProperty("spring.profiles.active", "dev");
        
        // 注册测试URL映射
        String testUrl = "************************************************************************************";
        P6SpyLogger.registerDatabaseMapping(testUrl, "YL_CONFIG");
        
        // 创建P6SpyLogger实例
        P6SpyLogger logger = new P6SpyLogger();
        
        // 测试格式化消息
        String result = logger.formatMessage(
            1,
            "2025-07-05 14:40:08",
            21,
            "statement",
            "",
            "select * from t_settings where setting_key = 'app.version'",
            testUrl
        );
        
        System.out.println("=== P6Spy日志格式测试 ===");
        System.out.println("原始输出：");
        System.out.println(result);
        
        // 测试彩色输出
        StdoutLogger stdoutLogger = new StdoutLogger();
        System.out.println("\n彩色输出：");
        stdoutLogger.logText(result);
        
        // 清理环境变量
        System.clearProperty("spring.profiles.active");
    }
    
    @Test
    public void testCallerInfoFromMapper() {
        // 模拟从Mapper调用的情况
        simulateMapperCall();
    }
    
    private void simulateMapperCall() {
        simulateServiceCall();
    }
    
    private void simulateServiceCall() {
        // 设置测试环境
        System.setProperty("spring.profiles.active", "dev");
        
        String testUrl = "************************************************************************************";
        P6SpyLogger.registerDatabaseMapping(testUrl, "YL_CONFIG");
        
        P6SpyLogger logger = new P6SpyLogger();
        
        String result = logger.formatMessage(
            1,
            "2025-07-05 14:40:08",
            21,
            "statement",
            "",
            "select * from t_settings where setting_key = ?",
            testUrl
        );
        
        System.out.println("=== 模拟Mapper调用测试 ===");
        System.out.println(result);
        
        StdoutLogger stdoutLogger = new StdoutLogger();
        stdoutLogger.logText(result);
        
        System.clearProperty("spring.profiles.active");
    }
}
