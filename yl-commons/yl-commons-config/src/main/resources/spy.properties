# 模块列表，根据版本选择合适的配置
modulelist=com.tuowan.yeliao.commons.config.p6spy.MybatisLogFactory,com.p6spy.engine.outage.P6OutageFactory

# 自定义日志格式
logMessageFormat=com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger

# 日志输出到控制台
appender=com.tuowan.yeliao.commons.config.p6spy.StdoutLogger

# 取消JDBC驱动注册
deregisterdrivers=true

# 使用前缀
useprefix=true

# 排除的日志类别
excludecategories=info,debug,result,commit,resultset

# 日期格式
dateformat=yyyy-MM-dd HH:mm:ss

# 实际驱动列表
# driverlist=org.h2.Driver

# 开启慢SQL记录
outagedetection=true

# 慢SQL记录标准（单位：秒）
outagedetectioninterval=2

filter=true
exclude=SELECT 1