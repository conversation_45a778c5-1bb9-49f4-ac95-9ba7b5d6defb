# P6Spy数据库名称映射配置说明

## 概述

P6Spy数据库名称映射功能已经从硬编码重构为通用的配置化方案，完全移除了特殊处理逻辑，支持灵活的配置方式和动态管理。

## 配置优先级

1. **配置文件** (`p6spy-database-mapping.properties`) - 最高优先级
2. **硬编码备用映射** - 兜底方案

## 配置方式

### 1. 配置文件方式（推荐）

在 `src/main/resources/p6spy-database-mapping.properties` 中配置：

```properties
# 精确匹配（优先级最高）- 基于nacos配置中的实际数据库名
yl_acct=YL_ACCT
yl_busi=YL_BUSI
yl_config=YL_CONFIG
yl_user=YL_USER
yl_social=YL_SOCIAL
yl_log=YL_LOG
yl_cms=YL_CMS
yl_rep=YL_REP

# 关键字匹配（用于模糊匹配）
acct=YL_ACCT
busi=YL_BUSI
config=YL_CONFIG
user=YL_USER
social=YL_SOCIAL
log=YL_LOG
cms=YL_CMS
rep=YL_REP
```

### 2. 代码动态配置

```java
// 注册单个映射
P6SpyMappingManager.registerMapping("my_db", "YL_CUSTOM");

// 批量注册映射
Map<String, String> mappings = new HashMap<>();
mappings.put("prod_config", "YL_CONFIG");
mappings.put("test_user", "YL_USER");
P6SpyMappingManager.registerBatchMappings(mappings);

// 注册URL映射
P6SpyMappingManager.registerUrlMapping(
    "******************************************", 
    "YL_CONFIG"
);
```

### 3. 在JdbcDataSource中自动注册

```java
@PostConstruct
public void init() {
    // 自动注册URL映射
    P6SpyLogger.registerDatabaseMapping(jdbcUrl, dbType.getSchema());
}
```

## 映射规则

### 精确匹配
- 物理数据库名完全匹配配置的key
- 优先级最高

### 模糊匹配
- 物理数据库名包含配置的key
- 用于处理带前缀/后缀的数据库名
- 例如：`dev_user_db` 会匹配到 `user=YL_USER`

### 默认处理
- 如果无法匹配任何规则，返回原始数据库名的大写形式
- 例如：`unknown_db` -> `UNKNOWN_DB`

## 调试和管理

### 查看当前映射状态

```java
// 打印所有映射关系
P6SpyMappingManager.printAllMappings();

// 获取映射统计
String stats = P6SpyMappingManager.getMappingStats();
System.out.println(stats);

// 检查映射是否正常配置
boolean configured = P6SpyMappingManager.isMappingConfigured();
```

### 测试映射关系

```java
// 测试特定数据库名的映射结果
String result = P6SpyMappingManager.testMapping("yl_config");
System.out.println("yl_config -> " + result); // 输出: yl_config -> YL_CONFIG
```

## 日志格式

配置完成后，SQL日志将显示正确的逻辑数据库名：

```
[SQL-DEV] 2025-07-05 14:40:08 | 执行时间: 21ms | 数据库: YL_CONFIG | 调用: UserMapper.selectById:45 | SQL: select * from t_user
```

## 最佳实践

1. **使用配置文件**：优先使用 `p6spy-database-mapping.properties` 配置文件
2. **环境特定配置**：可以为不同环境创建不同的配置文件
3. **动态配置**：对于运行时确定的数据库，使用代码动态注册
4. **调试验证**：使用 `P6SpyMappingManager` 工具类验证配置是否正确

## 注意事项

1. 配置文件中的key会自动转为小写进行匹配
2. 精确匹配优先于模糊匹配
3. 如果无法匹配任何规则，返回原始数据库名的大写形式
4. 映射关系在应用启动时加载，运行时修改配置文件不会生效
5. 可以通过代码动态添加映射关系，但无法删除已有映射
6. 环境名称直接使用spring.profiles.active的值，不做特殊映射

## 升级说明

从硬编码方式升级到通用配置化方式：

1. **移除特殊处理**：不再有YL_前缀特殊处理、shanghai特殊映射等
2. **环境名称通用化**：直接使用spring.profiles.active的值，支持任意环境名
3. **配置文件优先**：优先使用配置文件，备用映射仅作兜底
4. **完全向后兼容**：现有代码无需修改
5. **更加通用**：适用于任何项目，不限于特定的数据库命名规范
