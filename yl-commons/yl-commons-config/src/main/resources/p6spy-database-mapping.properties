# P6Spy数据库名称映射配置文件
# 格式：物理数据库名=逻辑数据库名
# 支持精确匹配和模糊匹配

# 精确匹配（优先级最高）- 基于nacos配置中的实际数据库名
yl_acct=YL_ACCT
yl_busi=YL_BUSI
yl_config=YL_CONFIG
yl_user=YL_USER
yl_social=YL_SOCIAL
yl_log=YL_LOG
yl_cms=YL_CMS
yl_rep=YL_REP

# 关键字匹配（用于模糊匹配）
acct=YL_ACCT
busi=YL_BUSI
config=YL_CONFIG
user=YL_USER
social=YL_SOCIAL
log=YL_LOG
cms=YL_CMS
rep=YL_REP

# 特殊处理：query和repQuery类型在nacos中没有指定具体数据库名
# 可以根据实际需要添加映射
# query=YL_QUERY
# repquery=YL_REP_QUERY

# 可以根据不同环境添加更多映射
# 例如：
# dev_yl_config=YL_CONFIG
# test_yl_user=YL_USER
# prod_yl_busi=YL_BUSI
