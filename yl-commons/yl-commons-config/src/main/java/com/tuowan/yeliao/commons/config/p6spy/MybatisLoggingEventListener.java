/*
 * Copyright (c) 2011-2025, b<PERSON><PERSON><PERSON><PERSON> (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.tuowan.yeliao.commons.config.p6spy;

import com.p6spy.engine.common.StatementInformation;
import com.p6spy.engine.logging.LoggingEventListener;

import java.sql.SQLException;

/**
 * 监听事件
 *
 * <AUTHOR>
 * @since 2019-11-10
 */
public class MybatisLoggingEventListener extends LoggingEventListener {
    private static MybatisLoggingEventListener INSTANCE;

    public static MybatisLoggingEventListener getInstance() {
        if (null == INSTANCE) {
            INSTANCE = new MybatisLoggingEventListener();
        }
        return INSTANCE;
    }

    @Override
    public void onAfterExecuteBatch(StatementInformation statementInformation, long timeElapsedNanos, int[] updateCounts, SQLException e) {
        //忽略批量执行结果
    }

}