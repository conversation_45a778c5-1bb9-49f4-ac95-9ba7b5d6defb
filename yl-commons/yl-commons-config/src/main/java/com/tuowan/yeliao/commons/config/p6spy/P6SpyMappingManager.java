package com.tuowan.yeliao.commons.config.p6spy;

import com.easyooo.framework.common.util.StringUtils;

import java.util.Map;

/**
 * P6Spy数据库名称映射管理器
 * 提供便捷的映射管理和调试功能
 * 
 * <AUTHOR>
 * @since 2025-07-05
 */
public class P6SpyMappingManager {

    /**
     * 批量注册物理数据库名到逻辑数据库名的映射
     * 
     * @param mappings 映射关系Map，key为物理数据库名，value为逻辑数据库名
     */
    public static void registerBatchMappings(Map<String, String> mappings) {
        if (mappings == null || mappings.isEmpty()) {
            return;
        }
        
        for (Map.Entry<String, String> entry : mappings.entrySet()) {
            String physicalName = entry.getKey();
            String logicalName = entry.getValue();
            
            if (StringUtils.isNotBlank(physicalName) && StringUtils.isNotBlank(logicalName)) {
                P6SpyLogger.registerPhysicalToLogicalMapping(physicalName, logicalName);
            }
        }
    }

    /**
     * 注册单个映射关系
     * 
     * @param physicalName 物理数据库名
     * @param logicalName 逻辑数据库名
     */
    public static void registerMapping(String physicalName, String logicalName) {
        P6SpyLogger.registerPhysicalToLogicalMapping(physicalName, logicalName);
    }

    /**
     * 注册URL映射关系
     * 
     * @param url JDBC URL
     * @param databaseName 数据库名称
     */
    public static void registerUrlMapping(String url, String databaseName) {
        P6SpyLogger.registerDatabaseMapping(url, databaseName);
    }

    /**
     * 打印当前所有映射关系（用于调试）
     */
    public static void printAllMappings() {
        System.out.println("=== P6Spy数据库名称映射状态 ===");
        
        System.out.println("\n1. 物理数据库名 -> 逻辑数据库名映射：");
        Map<String, String> physicalMappings = P6SpyLogger.getPhysicalToLogicalMappings();
        if (physicalMappings.isEmpty()) {
            System.out.println("   (无映射)");
        } else {
            for (Map.Entry<String, String> entry : physicalMappings.entrySet()) {
                System.out.printf("   %s -> %s%n", entry.getKey(), entry.getValue());
            }
        }
        
        System.out.println("\n2. URL -> 数据库名映射：");
        Map<String, String> urlMappings = P6SpyLogger.getUrlToDatabaseMappings();
        if (urlMappings.isEmpty()) {
            System.out.println("   (无映射)");
        } else {
            for (Map.Entry<String, String> entry : urlMappings.entrySet()) {
                System.out.printf("   %s -> %s%n", entry.getKey(), entry.getValue());
            }
        }
        
        System.out.println("================================");
    }

    /**
     * 测试映射关系
     * 
     * @param physicalDbName 物理数据库名
     * @return 映射后的逻辑数据库名
     */
    public static String testMapping(String physicalDbName) {
        if (StringUtils.isBlank(physicalDbName)) {
            return "UNKNOWN";
        }
        
        // 创建一个临时的P6SpyLogger实例来测试映射
        P6SpyLogger logger = new P6SpyLogger();
        
        // 使用反射调用私有方法进行测试
        try {
            java.lang.reflect.Method method = P6SpyLogger.class.getDeclaredMethod("mapToLogicalDatabaseName", String.class);
            method.setAccessible(true);
            return (String) method.invoke(logger, physicalDbName);
        } catch (Exception e) {
            return "ERROR: " + e.getMessage();
        }
    }

    /**
     * 清空所有映射关系（谨慎使用，主要用于测试）
     */
    public static void clearAllMappings() {
        // 注意：这里无法直接清空，因为映射缓存是私有的
        // 如果需要此功能，需要在P6SpyLogger中添加相应的方法
        System.out.println("警告：clearAllMappings功能需要在P6SpyLogger中添加支持");
    }

    /**
     * 检查映射配置是否正常加载
     */
    public static boolean isMappingConfigured() {
        Map<String, String> mappings = P6SpyLogger.getPhysicalToLogicalMappings();
        return !mappings.isEmpty();
    }

    /**
     * 获取映射统计信息
     */
    public static String getMappingStats() {
        Map<String, String> physicalMappings = P6SpyLogger.getPhysicalToLogicalMappings();
        Map<String, String> urlMappings = P6SpyLogger.getUrlToDatabaseMappings();
        
        return String.format("P6Spy映射统计: 物理映射=%d个, URL映射=%d个", 
                physicalMappings.size(), urlMappings.size());
    }
}
