package com.tuowan.yeliao.commons.config.p6spy;

/**
 * P6Spy通用配置演示
 * 展示移除特殊处理后的通用映射功能
 */
public class P6SpyUniversalDemo {

    public static void main(String[] args) {
        System.out.println("=== P6Spy 通用配置演示 ===\n");
        
        // 测试不同环境
        testEnvironment("dev");
        testEnvironment("test");
        testEnvironment("prod");
        testEnvironment("staging");  // 任意环境名
        testEnvironment("local");    // 任意环境名
        
        // 测试数据库映射
        testDatabaseMapping();
    }

    private static void testEnvironment(String env) {
        System.out.println("--- 测试环境: " + env + " ---");
        
        // 设置环境
        System.setProperty("spring.profiles.active", env);
        
        // 创建实例测试
        P6SpyLogger logger = new P6SpyLogger();
        
        // 模拟SQL日志
        String result = logger.formatMessage(
            1,
            "2025-07-05 15:30:00",
            25,
            "statement",
            "",
            "SELECT * FROM t_user WHERE user_id = ?",
            "*****************************************"
        );
        
        System.out.println("环境标签: [SQL-" + env.toUpperCase() + "]");
        System.out.println("完整日志: " + result);
        System.out.println();
        
        // 清理环境变量
        System.clearProperty("spring.profiles.active");
    }

    private static void testDatabaseMapping() {
        System.out.println("=== 数据库映射测试 ===");
        
        // 显示当前映射状态
        P6SpyMappingManager.printAllMappings();
        
        // 测试各种数据库名映射
        String[] testDatabases = {
            "yl_config",      // 精确匹配
            "yl_user",        // 精确匹配
            "yl_busi",        // 精确匹配
            "dev_yl_config",  // 模糊匹配
            "test_user_db",   // 模糊匹配
            "prod_busi_new",  // 模糊匹配
            "unknown_db",     // 无匹配，返回大写
            "my_custom_db"    // 无匹配，返回大写
        };
        
        System.out.println("\n数据库名映射测试结果：");
        for (String dbName : testDatabases) {
            String mapped = P6SpyMappingManager.testMapping(dbName);
            System.out.printf("  %-15s -> %s%n", dbName, mapped);
        }
        
        // 测试动态添加映射
        System.out.println("\n=== 动态映射测试 ===");
        P6SpyMappingManager.registerMapping("custom_db", "YL_CUSTOM");
        P6SpyMappingManager.registerMapping("special_db", "YL_SPECIAL");
        
        String customResult = P6SpyMappingManager.testMapping("custom_db");
        String specialResult = P6SpyMappingManager.testMapping("special_db");
        
        System.out.println("动态添加映射后：");
        System.out.printf("  %-15s -> %s%n", "custom_db", customResult);
        System.out.printf("  %-15s -> %s%n", "special_db", specialResult);
        
        // 显示最终映射统计
        System.out.println("\n" + P6SpyMappingManager.getMappingStats());
    }
}
