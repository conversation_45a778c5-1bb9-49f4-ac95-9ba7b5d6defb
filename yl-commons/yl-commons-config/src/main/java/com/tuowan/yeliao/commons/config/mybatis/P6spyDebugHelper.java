package com.tuowan.yeliao.commons.config.mybatis;

import com.easyooo.framework.common.util.PropUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.AppConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.P6spyConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/**
 * P6Spy调试辅助类
 * 用于诊断P6Spy配置问题
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
public class P6spyDebugHelper {

    private static final Logger log = LoggerFactory.getLogger(P6spyDebugHelper.class);

    /**
     * 打印P6Spy配置调试信息
     */
    public static void printDebugInfo() {
        log.info("=== P6Spy配置调试信息开始 ===");
        
        // 1. 检查当前环境
        log.info("当前环境: {}", AppConfig.ENV.name());
        log.info("是否生产环境: {}", UnifiedConfig.isProdEnv());
        log.info("是否开发环境: {}", UnifiedConfig.isDevEnv());
        log.info("是否测试环境: {}", UnifiedConfig.isTestEnv());
        
        // 2. 检查P6Spy配置状态
        log.info("P6Spy配置启用状态: {}", P6spyConfig.isEnabled());
        
        // 3. 检查P6Spy类是否存在
        boolean p6spyClassExists = false;
        try {
            Class.forName("com.p6spy.engine.spy.P6SpyDriver");
            p6spyClassExists = true;
            log.info("P6Spy驱动类: ✅ 已找到");
        } catch (ClassNotFoundException e) {
            log.info("P6Spy驱动类: ❌ 未找到 - {}", e.getMessage());
        }
        
        // 4. 检查配置文件
        boolean configFileExists = false;
        try {
            if (P6spyDebugHelper.class.getClassLoader().getResource("spy.properties") != null) {
                configFileExists = true;
                log.info("spy.properties配置文件: ✅ 已找到");
            } else {
                log.info("spy.properties配置文件: ❌ 未找到");
            }
        } catch (Exception e) {
            log.info("spy.properties配置文件检查异常: {}", e.getMessage());
        }
        
        // 5. 打印所有相关配置
        Properties props = UnifiedConfig.getProperties();
        log.info("所有p6spy相关配置:");
        props.stringPropertyNames().stream()
            .filter(key -> key.toLowerCase().contains("p6spy"))
            .forEach(key -> log.info("  {} = {}", key, props.getProperty(key)));
        
        // 6. 系统属性
        String spyPropertiesPath = System.getProperty("spy.properties");
        if (spyPropertiesPath != null) {
            log.info("系统属性spy.properties: {}", spyPropertiesPath);
        } else {
            log.info("系统属性spy.properties: 未设置");
        }
        
        // 7. 检查spring.profiles.active
        String springProfiles = System.getProperty("spring.profiles.active");
        if (springProfiles == null) {
            springProfiles = System.getenv("SPRING_PROFILES_ACTIVE");
        }
        log.info("Spring Profiles Active: {}", springProfiles);
        
        // 8. 检查PropUtils.getBoolean的调用
        try {
            boolean p6spyEnabledFromConfig = PropUtils.getBoolean(props, "p6spy", "enabled");
            log.info("从配置读取的p6spy.enabled值: {}", p6spyEnabledFromConfig);
        } catch (Exception e) {
            log.info("读取p6spy.enabled配置异常: {}", e.getMessage());
        }
        
        // 9. 综合判断
        if (P6spyConfig.isEnabled() && p6spyClassExists && configFileExists) {
            log.info("✅ P6Spy应该正常工作");
        } else {
            log.info("❌ P6Spy可能无法正常工作:");
            if (!P6spyConfig.isEnabled()) {
                log.info("  - P6Spy配置未启用");
            }
            if (!p6spyClassExists) {
                log.info("  - P6Spy依赖未找到");
            }
            if (!configFileExists) {
                log.info("  - spy.properties配置文件未找到");
            }
        }
        
        log.info("=== P6Spy配置调试信息结束 ===");
    }
    
    /**
     * 检查数据源是否使用了P6Spy驱动
     */
    public static void checkDataSourceDriver(String driverClassName, String jdbcUrl) {
        log.info("数据源驱动检查:");
        log.info("  驱动类: {}", driverClassName);
        log.info("  JDBC URL: {}", jdbcUrl);
        
        if ("com.p6spy.engine.spy.P6SpyDriver".equals(driverClassName)) {
            log.info("  ✅ 正在使用P6Spy驱动");
        } else {
            log.info("  ❌ 未使用P6Spy驱动");
        }
        
        if (jdbcUrl != null && jdbcUrl.startsWith("jdbc:p6spy:")) {
            log.info("  ✅ JDBC URL包含p6spy前缀");
        } else {
            log.info("  ❌ JDBC URL不包含p6spy前缀");
        }
    }
}
