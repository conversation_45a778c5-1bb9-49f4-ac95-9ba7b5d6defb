package com.tuowan.yeliao.commons.config.configuration.refresh.impl;

import com.tuowan.yeliao.commons.config.configuration.refresh.RefreshableConfig;
import com.tuowan.yeliao.commons.config.p6spy.P6SpyLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * P6Spy配置刷新实现
 * 支持动态更新P6Spy数据库名称映射配置
 *
 * <AUTHOR> Assistant
 * @date 2025-07-05
 */
@Component
public class P6SpyConfigRefreshImpl implements RefreshableConfig {

    private static final Logger LOG = LoggerFactory.getLogger(P6SpyConfigRefreshImpl.class);

    @Override
    public String dataId() {
        // 这里可以根据实际的nacos配置文件名进行调整
        return "p6spy-config.yaml";
    }

    @Override
    public void receiveConfigInfo(Properties props) {
        try {
            LOG.info("收到P6Spy配置更新，开始刷新数据库名称映射配置");
            
            // 重新加载P6Spy的数据库映射配置
            P6SpyLogger.reloadDatabaseMappings();
            
            LOG.info("P6Spy配置刷新完成");
        } catch (Exception e) {
            LOG.error("P6Spy配置刷新失败", e);
        }
    }
}
