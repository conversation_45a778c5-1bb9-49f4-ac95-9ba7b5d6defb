package com.tuowan.yeliao.commons.config.p6spy;

/**
 * P6SpyLogger测试类
 * 用于验证SQL日志格式化和颜色输出
 */
public class P6SpyLoggerTest {

    public static void main(String[] args) {
        // 设置不同的环境进行测试
        testWithEnvironment("dev");
        testWithEnvironment("test");
        testWithEnvironment("prod");
    }

    private static void testWithEnvironment(String env) {
        System.out.println("\n=== 测试环境: " + env.toUpperCase() + " ===");
        
        // 设置环境变量
        System.setProperty("spring.profiles.active", env);
        
        // 创建测试实例
        P6SpyLogger logger = new P6SpyLogger();
        StdoutLogger stdoutLogger = new StdoutLogger();
        
        // 测试不同类型的SQL语句
        String[] testSqls = {
            "SELECT * FROM t_user WHERE user_id = 12345",
            "INSERT INTO t_user (username, email) VALUES ('test', '<EMAIL>')",
            "UPDATE t_user SET username = 'newname' WHERE user_id = 12345",
            "DELETE FROM t_user WHERE user_id = 12345",
            "SELECT u.username, p.title FROM t_user u LEFT JOIN t_post p ON u.user_id = p.user_id WHERE u.status = 1 ORDER BY u.create_time DESC LIMIT 10"
        };
        
        for (int i = 0; i < testSqls.length; i++) {
            String formattedMessage = logger.formatMessage(
                i + 1,                           // connectionId
                "2025-07-04 10:30:15",          // now
                25 + i * 10,                    // elapsed
                "statement",                     // category
                "",                             // prepared
                testSqls[i],                    // sql
                "********************************" // url
            );
            
            // 输出带颜色的日志
            stdoutLogger.logText(formattedMessage);
        }
        
        // 清理系统属性
        System.clearProperty("spring.profiles.active");
    }
}
