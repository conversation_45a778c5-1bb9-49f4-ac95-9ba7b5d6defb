package com.tuowan.yeliao.commons.config.p6spy;

/**
 * P6Spy日志格式演示
 * 展示新的日志格式和颜色效果，包括调用信息
 */
public class P6SpyDemo {

    public static void main(String[] args) {
        System.out.println("=== P6Spy 新日志格式演示（包含调用信息）===\n");
        
        // 设置不同环境进行演示
        demonstrateFormat("dev", "************************************************************************************");
        demonstrateFormat("test", "******************************************************");
        demonstrateFormat("prod", "******************************************************");
    }

    private static void demonstrateFormat(String env, String url) {
        System.out.println("--- 环境: " + env.toUpperCase() + " ---");
        
        // 设置环境
        System.setProperty("spring.profiles.active", env);
        
        // 注册URL映射
        if (url.contains("shanghai")) {
            P6SpyLogger.registerDatabaseMapping(url, "YL_CONFIG");
        } else if (url.contains("user_db")) {
            P6SpyLogger.registerDatabaseMapping(url, "YL_USER");
        } else if (url.contains("busi_db")) {
            P6SpyLogger.registerDatabaseMapping(url, "YL_BUSI");
        }
        
        // 创建实例
        P6SpyLogger logger = new P6SpyLogger();
        StdoutLogger stdoutLogger = new StdoutLogger();
        
        // 演示不同类型的SQL
        String[] sqls = {
            "SELECT * FROM t_settings WHERE setting_key = 'app.version'",
            "INSERT INTO t_user (username, email, create_time) VALUES ('testuser', '<EMAIL>', NOW())",
            "UPDATE t_user SET last_login_time = NOW() WHERE user_id = 12345"
        };
        
        for (int i = 0; i < sqls.length; i++) {
            // 模拟从不同的方法调用
            if (i == 0) {
                simulateMapperCall(logger, stdoutLogger, sqls[i], url, i + 1);
            } else if (i == 1) {
                simulateServiceCall(logger, stdoutLogger, sqls[i], url, i + 1);
            } else {
                simulateManagerCall(logger, stdoutLogger, sqls[i], url, i + 1);
            }
        }
        
        System.out.println();
        
        // 清理环境变量
        System.clearProperty("spring.profiles.active");
    }
    
    private static void simulateMapperCall(P6SpyLogger logger, StdoutLogger stdoutLogger, String sql, String url, int connectionId) {
        String formatted = logger.formatMessage(
            connectionId,
            "2025-07-05 14:45:26",
            24 + connectionId * 5,
            "statement",
            "",
            sql,
            url
        );
        
        stdoutLogger.logText(formatted);
    }
    
    private static void simulateServiceCall(P6SpyLogger logger, StdoutLogger stdoutLogger, String sql, String url, int connectionId) {
        String formatted = logger.formatMessage(
            connectionId,
            "2025-07-05 14:45:27",
            24 + connectionId * 5,
            "statement",
            "",
            sql,
            url
        );
        
        stdoutLogger.logText(formatted);
    }
    
    private static void simulateManagerCall(P6SpyLogger logger, StdoutLogger stdoutLogger, String sql, String url, int connectionId) {
        String formatted = logger.formatMessage(
            connectionId,
            "2025-07-05 14:45:28",
            24 + connectionId * 5,
            "statement",
            "",
            sql,
            url
        );
        
        stdoutLogger.logText(formatted);
    }
}
