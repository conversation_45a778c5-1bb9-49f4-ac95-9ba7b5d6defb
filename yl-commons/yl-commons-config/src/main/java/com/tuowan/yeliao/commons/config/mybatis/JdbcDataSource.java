package com.tuowan.yeliao.commons.config.mybatis;


import com.easyooo.framework.common.util.PropUtils;
import com.tuowan.yeliao.commons.config.UnifiedConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.DataSourceConfig;
import com.tuowan.yeliao.commons.config.configuration.impl.P6spyConfig;
import com.tuowan.yeliao.commons.config.enums.DBType;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

public class JdbcDataSource extends HikariDataSource {

    private Logger log = LoggerFactory.getLogger(this.getClass());

    public JdbcDataSource(DBType dbType, DataSourceConfig config) {
        super();
        Properties props = config.getProps();
        String prefix = dbType.getPrefix();
        String jdbcUrl = PropUtils.getString(props, prefix, "jdbcUrlFcy");
        String driverClassName = PropUtils.getString(props, prefix, "driverClassName");

        // 检查是否启用p6spy（在代码中自动处理，无需配置）
        if (P6spyConfig.isEnabled()) {
            try {
                // 检查p6spy是否在classpath中
                Class.forName("com.p6spy.engine.spy.P6SpyDriver");

                // 自动使用p6spy驱动和URL前缀
                driverClassName = "com.p6spy.engine.spy.P6SpyDriver";
                if (!jdbcUrl.startsWith("jdbc:p6spy:")) {
                    // 移除原有的jdbc:前缀，然后添加p6spy前缀
                    if (jdbcUrl.startsWith("jdbc:")) {
                        jdbcUrl = "jdbc:p6spy:" + jdbcUrl.substring(5);
                    } else {
                        jdbcUrl = "jdbc:p6spy:" + jdbcUrl;
                    }
                }
            } catch (ClassNotFoundException e) {
                log.warn("P6Spy依赖未找到，使用原始数据库驱动: {}", e.getMessage());
            }
        }

        // 前缀作为数据源名称
        if (UnifiedConfig.isProdEnv()) {
            log.info("Initialize {} database：{}", dbType.getId(), "****");
        } else {
            log.info("Initialize {} database：{}", dbType.getId(), jdbcUrl);
        }

        this.setSchema(dbType.getSchema());
        this.setPoolName(dbType.getId());
        this.setJdbcUrl(jdbcUrl);
        this.setUsername(PropUtils.getString(props, prefix, "username"));
        this.setPassword(PropUtils.getString(props, prefix, "password"));
        this.setMaximumPoolSize(PropUtils.getInt(props, prefix, "maximumPoolSize"));
        this.setMinimumIdle(PropUtils.getInt(props, prefix, "minimumIdle"));
        this.setConnectionTimeout(PropUtils.getInt(props, prefix, "connectionTimeout"));
        this.setDriverClassName(driverClassName);
        this.setConnectionInitSql(PropUtils.getString(props, prefix, "connectionInitSql"));
    }
}
