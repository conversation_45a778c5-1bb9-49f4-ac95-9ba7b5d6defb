import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Properties;

/**
 * Simple test to verify p6spy functionality
 */
public class P6SpyTest {
    
    public static void main(String[] args) {
        try {
            // Test p6spy configuration
            testP6SpyConfiguration();
            
            // Test database connection with p6spy
            testDatabaseConnection();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static void testP6SpyConfiguration() {
        System.out.println("=== P6Spy Configuration Test ===");
        
        // Check if p6spy classes are available
        try {
            Class.forName("com.p6spy.engine.spy.P6SpyDriver");
            System.out.println("SUCCESS: P6Spy driver class found");
        } catch (ClassNotFoundException e) {
            System.out.println("ERROR: P6Spy driver class not found: " + e.getMessage());
            return;
        }
        
        // Check if spy.properties exists
        try {
            Properties props = new Properties();
            props.load(P6SpyTest.class.getClassLoader().getResourceAsStream("spy.properties"));
            System.out.println("SUCCESS: spy.properties found");
            System.out.println("  - driverlist: " + props.getProperty("driverlist"));
            System.out.println("  - logfile: " + props.getProperty("logfile"));
            System.out.println("  - appender: " + props.getProperty("appender"));
        } catch (Exception e) {
            System.out.println("ERROR: spy.properties not found or error reading: " + e.getMessage());
        }
        
        System.out.println();
    }
    
    private static void testDatabaseConnection() {
        System.out.println("=== Database Connection Test ===");
        
        // Test with p6spy driver
        String p6spyUrl = "*********************************************************************************************************************";
        String username = "root";
        String password = "Xchat@321#";
        
        try {
            System.out.println("Testing connection with P6Spy driver...");
            System.out.println("URL: " + p6spyUrl);
            
            Connection conn = DriverManager.getConnection(p6spyUrl, username, password);
            System.out.println("SUCCESS: P6Spy connection established");

            // Execute a simple query
            PreparedStatement stmt = conn.prepareStatement("SELECT 1 as test_value");
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                System.out.println("SUCCESS: Query executed successfully");
                System.out.println("  - test_value: " + rs.getInt("test_value"));
            }

            rs.close();
            stmt.close();
            conn.close();

            System.out.println("SUCCESS: Connection closed");

        } catch (Exception e) {
            System.out.println("ERROR: P6Spy connection failed: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
}
